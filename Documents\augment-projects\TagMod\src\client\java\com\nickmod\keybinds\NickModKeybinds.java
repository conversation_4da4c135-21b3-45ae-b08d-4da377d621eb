package com.nickmod.keybinds;

import net.fabricmc.fabric.api.client.keybinding.v1.KeyBindingHelper;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.client.util.InputUtil;
import org.lwjgl.glfw.GLFW;

/**
 * Manages keybindings for NickMod
 */
public class NickModKeybinds {
    private final KeyBinding openNickManager;
    private final KeyBinding quickNickSelect;
    private final KeyBinding applyLastNick;
    
    public NickModKeybinds() {
        this.openNickManager = new KeyBinding(
                "nickmod.keybind.open_nick_manager",
                InputUtil.Type.KEYSYM,
                GLFW.GLFW_KEY_N,
                "Nick Mod"
        );

        this.quickNickSelect = new KeyBinding(
                "nickmod.keybind.quick_nick_select",
                InputUtil.Type.KEYSYM,
                GLFW.GLFW_KEY_B,
                "Nick Mod"
        );

        this.applyLastNick = new KeyBinding(
                "nickmod.keybind.apply_last_nick",
                InputUtil.Type.KEYSYM,
                GLFW.GLFW_KEY_L,
                "Nick Mod"
        );
    }
    
    public KeyBinding getOpenNickManager() {
        return openNickManager;
    }
    
    public KeyBinding getQuickNickSelect() {
        return quickNickSelect;
    }
    
    public KeyBinding getApplyLastNick() {
        return applyLastNick;
    }
}
