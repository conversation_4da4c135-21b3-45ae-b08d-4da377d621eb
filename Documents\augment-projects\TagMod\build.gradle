plugins {
    id 'fabric-loom' version '1.7-SNAPSHOT'
    id 'maven-publish'
    id 'java'
}

version = project.mod_version
group = project.maven_group

base {
    archivesName = project.archives_base_name
}

repositories {
    // These repositories are required to resolve dependencies
    maven {
        name = 'ParchmentMC'
        url = 'https://maven.parchmentmc.org'
    }
    maven {
        name = 'IzzelAliz Maven'
        url = 'https://maven.izzel.io/releases/'
    }
    maven {
        name = 'TerraformersMC'
        url = 'https://maven.terraformersmc.com/releases/'
    }
    maven {
        name = 'Fuzs Mod Resources'
        url = 'https://raw.githubusercontent.com/Fuzss/modresources/main/maven/'
    }
    mavenCentral()
}

loom {
    // Enable access widener if needed
    // accessWidenerPath = file("src/main/resources/tagmod.accesswidener")
    
    // Generate run configurations for IDE
    runs {
        client {
            client()
            setConfigName("Minecraft Client")
            ideConfigGenerated(true)
            runDir("run")
        }
        server {
            server()
            setConfigName("Minecraft Server")
            ideConfigGenerated(true)
            runDir("run")
        }
    }
}

dependencies {
    // Minecraft and mappings
    minecraft "com.mojang:minecraft:${project.minecraft_version}"
    mappings loom.officialMojangMappings()

    // Fabric Loader and API
    modImplementation "net.fabricmc:fabric-loader:${project.loader_version}"
    modApi "net.fabricmc.fabric-api:fabric-api:${project.fabric_version}"

    // ModernUI dependencies
    implementation "icyllis.modernui:ModernUI-Core:${project.modernui_version}"
    implementation "icyllis.modernui:ModernUI-Markflow:${project.modernui_version}"
    
    // Include ModernUI in the mod jar
    include "icyllis.modernui:ModernUI-Core:${project.modernui_version}"
    include "icyllis.modernui:ModernUI-Markflow:${project.modernui_version}"

    // Optional: ModMenu integration for configuration screen
    modImplementation "com.terraformersmc:modmenu:${project.modmenu_version}"
    
    // Optional: Forge Config API Port for configuration compatibility
    modApi "fuzs.forgeconfigapiport:forgeconfigapiport-fabric:${project.forgeconfigapiport_version}"
}

processResources {
    inputs.property "version", project.version
    inputs.property "minecraft_version", project.minecraft_version
    inputs.property "loader_version", project.loader_version
    filteringCharset "UTF-8"

    filesMatching("fabric.mod.json") {
        expand "version": project.version,
                "minecraft_version": project.minecraft_version,
                "loader_version": project.loader_version
    }
}

def targetJavaVersion = 21
tasks.withType(JavaCompile).configureEach {
    // Ensure that this task is rerun when the sources change
    it.options.encoding = "UTF-8"
    if (targetJavaVersion >= 10 || JavaVersion.current().isJava10Compatible()) {
        it.options.release.set(targetJavaVersion)
    }
}

java {
    def javaVersion = JavaVersion.toVersion(targetJavaVersion)
    if (JavaVersion.current() < javaVersion) {
        toolchain.languageVersion = JavaLanguageVersion.of(targetJavaVersion)
    }
    archivesBaseName = project.archives_base_name
    // Loom will automatically attach sourcesJar to a RemapSourcesJar task and to the "build" task
    // if it is present.
    // If you remove this line, sources will not be generated.
    withSourcesJar()
}

jar {
    from("LICENSE") {
        rename { "${it}_${project.archives_base_name}"}
    }
}

// Configure the maven publication
publishing {
    publications {
        maven(MavenPublication) {
            from components.java
        }
    }

    // See https://docs.gradle.org/current/userguide/publishing_maven.html for information on how to set up publishing.
    repositories {
        // Add repositories to publish to here.
        // Notice: This block does NOT have the same function as the block in the top level.
        // The repositories here will be used for publishing your artifact, not for
        // resolving dependencies.
    }
}
