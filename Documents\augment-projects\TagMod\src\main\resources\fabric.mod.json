{"schemaVersion": 1, "id": "tagmod", "version": "${version}", "name": "TagMod", "description": "A Minecraft mod demonstrating ModernUI integration with Fabric", "authors": ["YourName"], "contact": {"homepage": "https://example.com/", "sources": "https://github.com/example/tagmod"}, "license": "MIT", "icon": "assets/tagmod/icon.png", "environment": "client", "entrypoints": {"client": ["com.example.tagmod.TagModClient"], "main": ["com.example.tagmod.TagMod"], "modmenu": ["com.example.tagmod.integration.ModMenuIntegration"]}, "mixins": ["tagmod.mixins.json"], "depends": {"fabricloader": ">=${loader_version}", "minecraft": "${minecraft_version}", "java": ">=21", "fabric-api": "*"}, "suggests": {"modmenu": "*"}, "custom": {"modmenu": {"badges": ["client"], "parent": {"id": "modernui", "name": "Modern UI", "badges": ["library"], "description": "Modern desktop framework for Minecraft"}}}}